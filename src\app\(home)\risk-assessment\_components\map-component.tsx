"use client";
import React, { useEffect } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  useMap,
  useMapEvents,
  LayersControl,
} from "react-leaflet";
import L from "leaflet";
import "leaflet/dist/leaflet.css";
import markerIcon2x from "leaflet/dist/images/marker-icon-2x.png";
import markerIcon from "leaflet/dist/images/marker-icon.png";
import markerShadow from "leaflet/dist/images/marker-shadow.png";

// Fix for default markers in react-leaflet
delete (L.Icon.Default.prototype as unknown as { _getIconUrl?: unknown })
  ._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: markerIcon2x,
  iconUrl: markerIcon,
  shadowUrl: markerShadow,
});

const customIcon = new L.Icon({
  iconUrl:
    "https://cdn.rawgit.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-red.png",
  shadowUrl:
    "https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png",
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41],
});

interface LocationData {
  lat: number;
  lng: number;
  name: string;
}

// Helper component to update map on click
const LocationPicker = ({
  onSelect,
}: {
  onSelect: (latlng: { lat: number; lng: number }) => void;
}) => {
  useMapEvents({
    click(e) {
      onSelect({ lat: e.latlng.lat, lng: e.latlng.lng });
    },
  });
  return null;
};

function MapUpdater({ location }: { location: LocationData }) {
  const map = useMap();

  useEffect(() => {
    map.setView([location.lat, location.lng], 17, {
      animate: true,
    });
  }, [location, map]);

  return null;
}

interface MapComponentProps {
  location: LocationData;
  onMapClick: (latlng: { lat: number; lng: number }) => void;
}

function MapComponent({ location, onMapClick }: MapComponentProps) {
  return (
    <MapContainer
      center={[location.lat, location.lng]}
      zoom={12}
      scrollWheelZoom
      className="w-full h-full min-h-[300px] sm:min-h-[400px] md:min-h-[500px] lg:min-h-full bg-gray-100 rounded-3xl"
    >
      <LayersControl position="topright">
        {/* Satellite View */}
        <LayersControl.BaseLayer checked name="Satellite View">
          <TileLayer
            attribution="© Google"
            url="https://mts1.google.com/vt/lyrs=m&x={x}&y={y}&z={z}"
          />
        </LayersControl.BaseLayer>

        {/* Street View */}
        <LayersControl.BaseLayer name="Street View">
          <TileLayer
            attribution="© Google"
            url="https://mt1.google.com/vt/lyrs=y&x={x}&y={y}&z={z}"
          />
        </LayersControl.BaseLayer>
      </LayersControl>

      {/* Map content shown regardless of selected base layer */}
      <Marker position={[location.lat, location.lng]} icon={customIcon}>
        <Popup>{location.name}</Popup>
      </Marker>
      <LocationPicker onSelect={onMapClick} />
      <MapUpdater location={location} />
    </MapContainer>
  );
}

export default MapComponent;
